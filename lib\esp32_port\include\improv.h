#pragma once
#include <Arduino.h>
#include <Preferences.h>
#include <WiFi.h>
#include <NimBLEDevice.h>
#include <NimBLEServer.h>
#include <NimBLEUtils.h>
#include <vector>

namespace improv
{

    static const uint8_t IMPROV_SERIAL_VERSION = 1;
    static const uint16_t IMPROV_MTU_SIZE = 512;

    enum Error : uint8_t
    {
        ERROR_NONE = 0x00,
        ERROR_INVALID_RPC = 0x01,
        ERROR_UNKNOWN_RPC = 0x02,
        ERROR_UNABLE_TO_CONNECT = 0x03,
        ERROR_NOT_AUTHORIZED = 0x04,
        ERROR_UNKNOWN = 0xFF,
    };

    enum State : uint8_t
    {
        STATE_STOPPED = 0x00,
        STATE_AWAITING_AUTHORIZATION = 0x01,
        STATE_AUTHORIZED = 0x02,
        STATE_PROVISIONING = 0x03,
        STATE_PROVISIONED = 0x04,
    };

    enum Command : uint8_t
    {
        COMMAND_IDENTIFY = 0x01,
        COMMAND_GET_CURRENT_STATE = 0x02,
        COMMAND_WIFI_SETTINGS = 0x03,
        COMMAND_GET_DEVICE_INFO = 0x04,
        COMMAND_GET_WIFI_NETWORKS = 0x05,
        COMMAND_BAD_CHECKSUM = 0xFF,
    };

    static const char *const SERVICE_UUID = "00467768-**************-************";
    static const char *const STATUS_UUID = "00467768-**************-************";
    static const char *const ERROR_UUID = "00467768-**************-************";
    static const char *const RPC_COMMAND_UUID = "00467768-**************-************";
    static const char *const RPC_RESULT_UUID = "00467768-**************-************";
    static const char *const CAPABILITY_UUID = "00467768-**************-************";

    // 前向声明
    class RPCCommandCallback;

    struct WiFiCredential {
        std::string ssid;
        std::string password;
        bool operator==(const WiFiCredential& other) const {
            return ssid == other.ssid;
        }
    };

    class ImprovComponent
    {
    public:
        void setup();
        void loop();
        void set_state(improv::State state);
        void set_error(improv::Error error);
        bool wifi_connect(const std::string &ssid, const std::string &password);
        bool isWifiConnected() {
            return (WiFi.status() == WL_CONNECTED);
        }        

        std::string device_name;
        std::string device_version;
        std::string device_url;

        // 声明友元类
        friend class RPCCommandCallback;

        static void wifiMonitorTask(void* parameter);
        void saveWiFiCredential(const std::string& ssid, const std::string& password);
        bool connectToSavedNetworks();
        void loadSavedNetworks();
        void stopBLEService();

        private:
        static constexpr size_t MAX_SAVED_NETWORKS = 5;
        std::vector<WiFiCredential> saved_networks_;
        static ImprovComponent* instance_;  // 单例指针
        
        // WiFi监控任务相关
        TaskHandle_t wifi_monitor_task_handle_ = nullptr;
        static constexpr uint32_t WIFI_MONITOR_STACK_SIZE = 4096;
        static constexpr UBaseType_t WIFI_MONITOR_PRIORITY = 1;
        static constexpr BaseType_t WIFI_MONITOR_CORE = 0;
    protected:
        NimBLEServer *server_{nullptr};
        NimBLEService *service_{nullptr};
        NimBLECharacteristic *status_characteristic_{nullptr};
        NimBLECharacteristic *error_characteristic_{nullptr};
        NimBLECharacteristic *rpc_command_characteristic_{nullptr};
        NimBLECharacteristic *rpc_result_characteristic_{nullptr};
        NimBLECharacteristic *capabilities_characteristic_{nullptr};

        improv::State state_{improv::STATE_STOPPED};
        improv::Error error_{improv::ERROR_NONE};

        void handle_wifi_settings_(const std::vector<uint8_t> &payload);
        void handle_identify_();
        void handle_device_info_();
        void on_state_change_();
        void on_error_change_();
        uint8_t calculateChecksum(const std::vector<uint8_t> &data, size_t length);
    };

} // namespace improv