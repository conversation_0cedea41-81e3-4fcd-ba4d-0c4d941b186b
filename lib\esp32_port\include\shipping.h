#ifdef __cplusplus
extern "C" {
#endif
    bool should_enter_shipping_mode();
    void clear_shipping_mode();
    void clear_wifi_info();
    void clear_tuya_activated_info();
    void factory_reset();
    void light_sleep();
    void deep_sleep();
    void init_wakeup_buttons();
    bool check_wakeup_buttons();
    bool is_button6_pressed();
    bool is_button7_pressed();
    void wait_button_release();
    bool check_long_press_wakeup_nonblocking();
    bool is_long_press_triggered();
    void reset_wakeup_request();
    bool check_deep_sleep_wakeup();
#ifdef __cplusplus
}
#endif